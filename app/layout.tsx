import type { Metadata } from "next";
import "./globals.css";
import Header from "@components/header";
import Footer from "@components/footer";
import MobileHeader from "@components/header/mobile-header";
import MobileFooter from "@components/footer/mobile-footer";

export const metadata: Metadata = {
  title: "iKonnected",
  icons: {
    icon: "/icons/logo.svg", // or "/icons/logo.svg" if that's where your file is
  },
  description: "Innovating Web3 underline icon, Empowering Developers",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="min-h-screen overflow-x-hidden">
        <Header />
        <MobileHeader />
        <main className="max-w-[2056px] mx-auto">{children}</main>
        <MobileFooter />
        <Footer />
      </body>
    </html>
  );
}