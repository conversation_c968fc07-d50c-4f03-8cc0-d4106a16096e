"use client";
import { useRef, useState, useEffect, MouseEvent, useContext } from "react";
import { Popper } from "@mui/material";
import { getClassNames } from "@/app/utils/helper";
import { usePathname } from "next/navigation";
import useScrollPosition from "@/app/utils/hooks/use-scroll-position";
// import { MainContext } from "@contexts/main-context";

interface ContextPopoverProps {
  placeholder: React.ReactNode;
  children: React.ReactNode;
  onVisibilityChange?: (isVisible: boolean) => void;
  containerStyle: string;
}

export default function ContextPopover({
  placeholder,
  children,
  onVisibilityChange,
  containerStyle,
}: ContextPopoverProps) {
  const hasScrolled = useScrollPosition();
  const [contextAnchorEl, setContextAnchorEl] = useState<null | HTMLElement>(
    null
  );
  const cancelButtonRef = useRef<HTMLDivElement>(null);
  const contextRef = useRef<HTMLDivElement>(null);

  const contextClick = (event: MouseEvent<HTMLDivElement>) => {
    setContextAnchorEl(contextAnchorEl ? null : event.currentTarget);
  };

  const handleClose = () => {
    setContextAnchorEl(null);
  };

  const pathname = usePathname();

  useEffect(() => {
    if (onVisibilityChange) {
      onVisibilityChange(Boolean(contextAnchorEl));
    }
  }, [contextAnchorEl, onVisibilityChange]);

  useEffect(() => {
    const outsideClick: EventListener = (e: Event) => {
      if ("target" in e) {
        if (
          cancelButtonRef.current instanceof Element &&
          cancelButtonRef.current.contains(e.target as Node)
        ) {
          return;
        }

        if (
          cancelButtonRef.current &&
          !cancelButtonRef.current.contains(e.target as Node) &&
          contextRef.current &&
          !contextRef.current.contains(e.target as Node)
        ) {
          //   setIsPopoverDropdownVisible(false);
          handleClose();
        }
      }
    };

    document.addEventListener("click", outsideClick);

    return () => {
      document.removeEventListener("click", outsideClick);
    };
  }, []);

  useEffect(() => {
    if (hasScrolled) {
      handleClose();
    }
    handleClose();
  }, [pathname, hasScrolled]);

  const open = Boolean(contextAnchorEl);
  const id = open ? "simple-popover" : undefined;

  return (
    <>
      <div className="" onClick={contextClick} ref={cancelButtonRef}>
        {placeholder}
      </div>
      <Popper
        sx={{
          marginTop: 1,
        }}
        id={id}
        open={open}
        anchorEl={contextAnchorEl}
        className="relative z-[1000]"
      >
        <div
          className={getClassNames(
            " rounded-lg bg-white shadow absolute",
            `${containerStyle}`
          )}
          ref={contextRef}
        >
          {children}
        </div>
      </Popper>
    </>
  );
}
