import ButtonText from "@components/text/button";
import Image, { StaticImageData } from "next/image";

type Props = {
  iconPosition: "left" | "right";
  icon: string | StaticImageData;
  text: string;
};

export default function IconTextButton({ iconPosition, icon, text }: Props) {
  return (
    <button className="flex gap-2 items-center text-grey-one text-lg leading-[21.09px] ">
      {iconPosition === "left" && <Image src={icon} alt="" />}
      <ButtonText text={text} />
      {iconPosition === "right" && <Image src={icon} alt="" />}
    </button>
  );
}