// components
import ButtonImage1 from "@public/images/google.png";
import ButtonImage2 from "@public/images/apple.png";
import Image from "next/image";

type Props = {
  buttonOneStyle: string;
  buttonTwoStyle: string;
};

export default function AppDownloadButtons({
  buttonOneStyle,
  buttonTwoStyle,
}: Props) {
  return (
    <div className="flex gap-x-2 items-center cursor-pointer">
      <Image
        src={ButtonImage1}
        alt="socials button"
        className={`${buttonOneStyle}`}
        priority
      />
      <Image
        src={ButtonImage2}
        alt="socials button"
        className={`${buttonTwoStyle}`}
        priority
      />
    </div>
  );
}
