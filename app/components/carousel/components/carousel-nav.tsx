import { MutableRefObject } from "react";
import Carousel from "react-multi-carousel";
// icons
import NavLeft from "@public/icons/nav-chevron-left.svg";
import NavRight from "@public/icons/nav-chevron-right.svg";

// components
import IconImage from "@components/icon-image";
import { StaticImageData } from "next/image";

type Props = {
  carouselRef: MutableRefObject<Carousel | null>;
  className?: string;
  features: {
    title: string;
    body: string;
    background?: string;
    image: StaticImageData;
  }[];
};

export default function CarouselNav({
  carouselRef,
  className,
  features,
}: Props) {
  return (
    <div className="relative">
      {" "}
      <div className="flex items-center gap-x-[18px] ">
        <button
          onClick={() => {
            if (carouselRef.current) {
              const prevSlide = carouselRef.current.state.currentSlide - 1;
              if (prevSlide !== -1) {
                carouselRef.current.goToSlide(prevSlide);
              }
            }
          }}
        >
          <IconImage
            src={NavLeft}
            alt="nav icon"
            imageStyle="w-6 h-6 md:w-[45px] md:h-[45px]"
          />
        </button>

        <button
          onClick={() => {
            if (carouselRef.current) {
              const ItemsToSHow = carouselRef.current.state.slidesToShow;

              const nextSlide = carouselRef.current.state.currentSlide + 1;

              const lastIndex = features.length - 1;

              if (ItemsToSHow === 3 && nextSlide <= lastIndex - 2) {
                carouselRef.current.goToSlide(nextSlide);
              } else if (ItemsToSHow === 2 && nextSlide <= lastIndex - 1) {
                carouselRef.current.goToSlide(nextSlide);
              } else if (ItemsToSHow === 1.5 && nextSlide <= lastIndex) {
                carouselRef.current.goToSlide(nextSlide);
              }
            }
          }}
        >
          <IconImage
            src={NavRight}
            alt="nav icon"
            imageStyle="w-6 h-6 md:w-[45px] md:h-[45px]"
          />
        </button>
      </div>
    </div>
  );
}
