import BodySmall from "@components/text/body/body-small";
import { getClassNames } from "@/app/utils/helper";
import TitleSmall from "@components/text/title/title-small";
import Image, { StaticImageData } from "next/image";

type Props = {
  title: string;
  image: StaticImageData;
  bodyHeader?: string;
  body: string;
  cardStyle?: string;
  background?: string;
  imgWidth?: number;
  imgHeight?: number;
  imgStyle?: string;
  imgContainerStyle?: string;
};
export default function FeaturesCard({
  title,
  image,
  bodyHeader,
  body,
  background,
  cardStyle,
  imgWidth,
  imgHeight,
  imgStyle,
  imgContainerStyle,
}: Props) {
  return (
    <div
      className={getClassNames(
        "space-y-8 lg:space-y-12 xl:space-y-10 md:pb-8",

        `${cardStyle}`
      )}
    >
      <div className="whitespace-nowrap">
        {" "}
        <TitleSmall
          text={title}
          className={getClassNames(
            "text-left text-dark-brown lg:text-2xl xl:text-[26px]",
            "font-title-semiBold white"
          )}
        />
      </div>

      <div className={` ${background} ${imgContainerStyle} `}>
        <Image
          src={image}
          alt="carousel image"
          width={imgWidth}
          height={imgHeight}
          className={`${imgStyle}`}
        />
      </div>

      <div className="space-y-4">
        <div>
          {" "}
          <TitleSmall
            text={bodyHeader}
            className="text-base md:text-lg lg:text-2xl text-blue"
          />
        </div>
        <BodySmall text={body} className=" leading-[160%]" />
      </div>
    </div>
  );
}
