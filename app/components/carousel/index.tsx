import { MutableRefObject, useEffect, useRef } from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";
import { responsive } from "@/app/utils/data";
import { StaticImageData } from "next/image";
import FeaturesCard from "./components/features-card";

type Props = {
  carouselRef: MutableRefObject<Carousel | null>;
  features: {
    title: string;
    body: string;
    bodyHeader?: string;
    background?: string;
    image: StaticImageData;
  }[];
  cardStyle?: string;
  cardContainerStyle?: string;
  imgWidth?: number;
  imgHeight?: number;
  imgStyle?: string;

  imgContainerStyle?: string;
};

export default function CarouselComponent({
  carouselRef,
  features,
  imgWidth,
  imgHeight,
  cardStyle,
  cardContainerStyle,
  imgContainerStyle,
  imgStyle,
}: Props) {
  let firstClientX: number;
  let clientX: number;

  const preventTouch = (e: TouchEvent) => {
    const minValue = 5; // threshold

    clientX = e.touches[0].clientX - firstClientX;

    // Vertical scrolling does not work when you start swiping horizontally.
    if (Math.abs(clientX) > minValue) {
      e.preventDefault();

      return false;
    }
  };

  const touchStart = (e: TouchEvent) => {
    firstClientX = e.touches[0].clientX;
  };

  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener("touchstart", touchStart as EventListener);
      container.addEventListener(
        "touchmove",
        preventTouch as EventListener,
        {
          passive: false,
        } as AddEventListenerOptions
      );
    }

    return () => {
      if (container) {
        container.removeEventListener(
          "touchstart",
          touchStart as EventListener
        );
        container.removeEventListener(
          "touchmove",
          preventTouch as EventListener,
          {
            passive: false,
          } as AddEventListenerOptions
        );
      }
    };
  }, []);
  return (
    <div ref={containerRef}>
      {" "}
      <Carousel
        arrows={false}
        containerClass="carousel-container"
        ref={carouselRef}
        responsive={responsive}
        swipeable={true}
        keyBoardControl={true}
        draggable={true}
      >
        {features.map(
          ({ title, background, body, bodyHeader, image }, index) => (
            <div key={index} className={`${cardContainerStyle}`}>
              <FeaturesCard
                title={title}
                bodyHeader={bodyHeader}
                body={body}
                image={image}
                background={background}
                imgHeight={imgHeight}
                imgWidth={imgWidth}
                imgContainerStyle={imgContainerStyle}
                imgStyle={imgStyle}
                cardStyle={cardStyle}
              />
            </div>
          )
        )}
      </Carousel>
    </div>
  );
}
