import Image from "next/image";
//icons
import LetterLogo from "@public/icons/logo.svg";
// components
import UsefulLinksList from "./components/useful-links-list";
import Socials from "./components/socials";
import CopyRight from "./components/copyright";
import AppDownloadButtons from "../buttons/app-download-buttons";

// utils
import { resources } from "@/app/utils/data";
import { getClassNames } from "@/app/utils/helper";

export default function Footer() {
  return (
    <section className="hidden md:block bg-dark-brown-100  py-20 ">
      <div className="w-11/12 mx-auto max-w-[2056px] ">
        <div
          className={getClassNames(
            "flex justify-between items-end pb-8",
            "border-b-[0.7px] border-b-light-grey px-6"
          )}
        >
          <div className="space-y-9">
            <div className="flex gap-x-16">
              <UsefulLinksList title="Resources" data={resources} />
            </div>

            <Socials />
          </div>

          <div className="gap-y-8 flex flex-col items-end">
            <Image src={LetterLogo} alt="Logo" width={110} priority />
            <AppDownloadButtons
              buttonOneStyle="w-[150px] lg:w-[191px]"
              buttonTwoStyle="w-[150px] lg:w-[191px]"
            />

          </div>
        </div>

        <CopyRight />
      </div>
    </section>
  );
}
