import Image from "next/image";
//icons
import Logo from "@public/icons/logo.svg";
// components
import UsefulLinksList from "./components/useful-links-list";
import Socials from "./components/socials";
import CopyRight from "./components/copyright";
import AppDownloadButtons from "../buttons/app-download-buttons";
// utils
import { resources } from "@/app/utils/data";
import { getClassNames } from "@/app/utils/helper";

export default function MobileFooter() {
  return (
    <section className=" bg-dark-brown-100 py-8 md:hidden">
      <div className="w-11/12 mx-auto">
        <div
          className={getClassNames(
            "flex gap-x-20  pb-8",
            "border-b-[0.7px] border-b-black-300 "
          )}
        >
          <UsefulLinksList title="Resources" data={resources} />
        </div>

        <div className="py-6 space-y-6">
          <Socials />
          <AppDownloadButtons
            buttonOneStyle="w-[72px] h-8"
            buttonTwoStyle="w-[72px] h-8"
          />
        </div>

        <CopyRight />
      </div>
    </section>
  );
}
{
  /* 

         

            
          </div> */
}