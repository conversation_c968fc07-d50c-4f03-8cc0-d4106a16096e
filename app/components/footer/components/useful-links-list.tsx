import Link from "next/link";

interface LinkItem {
  text: string;
  link: string;
}
type Props = {
  title: string;
  data: LinkItem[];
};

export default function UsefulLinksList({ title, data }: Props) {
  return (
    <ul className="space-y-6">
      <li className="text-lg md:text-xl md:tracking-[0.571px] lg:text-2xl text-orange font-title-bold ">
        {title}
      </li>
      {data.map(({ text, link }, index) => (
        <div key={index} className="cursor-pointer">
          <Link href={link}>
            <li className="text-base md:text-lg md:tracking-[0.571px] lg:text-xl text-white hover:text-creme-200  font-title-semiBold">
              {text}
            </li>
          </Link>
        </div>
      ))}
    </ul>
  );
}