import { socials } from "@/app/utils/data";
import Image from "next/image";
import Link from "next/link";

export default function Socials() {
  return (
    <div className="flex items-center gap-x-4 md:gap-x-6 lg:gap-x-8">
      {socials.map(({ icon, link }, index) => (
        <div key={index}>
          <Link href={link} target="_blank">
            {" "}
            <Image
              src={icon}
              alt="social Logo"
              className="w-5 h-5 md:w-7 md:h-7 lg:w-9 lg:h-9 cursor-pointer"
            />
          </Link>
        </div>
      ))}
    </div>
  );
}
