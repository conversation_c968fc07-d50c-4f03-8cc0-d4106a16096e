import { getClassNames } from "@/app/utils/helper";
import Image, { StaticImageData } from "next/image";

type Props = {
  source: string | StaticImageData;
  alt: string;
  widthClass: string;
  heightClass: string;
};

export default function ImageComponent({
  source,
  alt,
  widthClass,
  heightClass,
}: Props) {
  return (
    <Image
      src={source}
      alt={alt}
      className={getClassNames(`${widthClass} ${heightClass}`)}
      priority={true}
    />
  );
}