import { getClassNames } from "@/app/utils/helper";
import { ReactNode } from "react";

type Props = {
  text: string | ReactNode;
  className?: string;
};

export default function Title({ text, className }: Props) {
  return (
    <h1
      className={getClassNames(
        "text-[64px] text-center text-dark-brown",
        "font-title-bold leading-[127%] ",
        `${className}`
      )}
    >
      {text}
    </h1>
  );
}