import { getClassNames } from "@/app/utils/helper";
import { ReactNode } from "react";

type Props = { text: string | ReactNode; className?: string };

export default function TitleMedium({ text, className }: Props) {
  return (
    <h1
      className={getClassNames(
        "text-xl md:text-2xl lg:text-[42px] text-dark-brown",
        "font-title-bold leading-[130%] md:leading-[130%] lg:leading-[130%]",
        `${className}`
      )}
    >
      {text}
    </h1>
  );
}