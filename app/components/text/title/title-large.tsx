import { getClassNames } from "@/app/utils/helper";
import { ReactNode } from "react";

type Props = {
  text: string | ReactNode;
  className?: string;
};

export default function TitleLarge({ text, className }: Props) {
  return (
    <h1
      className={getClassNames(
        "text-2xl text-dark-brown",
        "font-title-bold leading-[115%] tracking-[0.571px]",
        "md:text-4xl lg:text-[58px] md:leading-[115%]",
        "md:tracking-[0.571px] lg:leading-[115%] lg:tracking-[0.571px]",
        `${className}`
      )}
    >
      {text}
    </h1>
  );
}