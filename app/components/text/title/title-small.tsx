import { ReactNode } from "react";
// utils
import { getClassNames } from "@/app/utils/helper";

type Props = {
  text: string | ReactNode;
  className?: string;
};

export default function TitleSmall({ text, className }: Props) {
  return (
    <h1
      className={getClassNames(
        "text-base md:text-xl lg:text-3xl  text-dark-brown",
        "font-title-bold leading-[127%] md:leading-[150%] lg:leading-[150%]",
        `${className}`
      )}
    >
      {text}
    </h1>
  );
}