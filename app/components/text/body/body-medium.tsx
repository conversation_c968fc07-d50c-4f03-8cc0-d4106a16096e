import { getClassNames } from "@/app/utils/helper";
import { ReactNode } from "react";

type Props = {
  text: string | ReactNode;
  className?: string;
};

export default function BodyMedium({ text, className }: Props) {
  return (
    <p
      className={getClassNames(
        "text-2xl text-dark-brown leading-[160%] md:leading-[160%] lg:leading-[160%]",
        "font-body-semiBold tracking-[0.571px] ",
        `${className}`
      )}
    >
      {" "}
      {text}
    </p>
  );
}