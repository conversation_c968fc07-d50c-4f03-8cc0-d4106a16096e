"use client";
import { getClassNames } from "@/app/utils/helper";
import { usePathname } from "next/navigation";
import { ReactNode } from "react";

type Props = {
  text: React.ReactNode;
  className?: string;
};

export default function BodySmall({ className, text }: Props) {
  return (
    <p
      className={getClassNames(
        "text-base md:text-lg lg:text-xl text-dark-brown",
        "font-body-semiBold tracking-[0.571px] leading-[160%] md:leading-[160%] lg:leading-[160%]",
        `${className}`
      )}
    >
      {" "}
      {text}
    </p>
  );
}