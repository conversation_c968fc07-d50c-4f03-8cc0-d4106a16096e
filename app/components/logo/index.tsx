import { getClassNames } from "@/app/utils/helper";
import LogoImage from "@public/images/logo.png";
import Image from "next/image";
import Link from "next/link";

type Props = {
  widthClass: string;
  heightClass: string;
};

export default function Logo({ widthClass, heightClass }: Props) {
  return (
    <div>
      <Link href="/">
        {" "}
        <Image
          src={LogoImage}
          alt="iKonnected Logo"
          className={getClassNames(`${widthClass} ${heightClass}`)}
        />
      </Link>
    </div>
  );
}
