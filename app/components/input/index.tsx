import { getClassNames } from "@/app/utils/helper";

type Props = {
  name: string;
  type: string;
  placeholder?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  id?: string;
  className?: string;
  value?: string;
};

export default function Input({
  name,
  type,
  placeholder,
  id,
  className,
  onChange,
  value,
}: Props) {
  return (
    <div>
      {" "}
      <input
        name={name}
        id={id}
        type={type}
        placeholder={placeholder}
        className={getClassNames(
          "w-full border-b border-b-grey-two placeholder:font-body placeholder:text-sm",
          "placeholder:text-center placeholder:leading-5 placeholder:text-placeholder-grey",
          "ring-0 focus:outline-none focus:ring-0 bg-transparent",
          `${className}`
        )}
        value={value}
        onChange={onChange}
        // onFocus={props.onFocus}
        // disabled={props.disabled}
      />
    </div>
  );
}