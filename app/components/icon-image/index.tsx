import { getClassNames } from "@/app/utils/helper";
import Image, { StaticImageData } from "next/image";
import React from "react";

type Props = {
  src: string;
  alt: string;
  imageStyle?: string;
  width?: number;
  height?: number;
};

export default function IconImage({
  src,
  alt,
  imageStyle,
  width,
  height,
}: Props) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width && width}
      height={height && height}
      priority={true}
      className={getClassNames("-z-10", `${imageStyle}`)}
    />
  );
}