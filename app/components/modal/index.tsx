"use client";

import { useContext, useEffect, useRef, useState } from "react";
import Modal from "@mui/material/Modal";
import Fade from "@mui/material/Fade";
// hooks
import useScreensize from "@/app/utils/hooks/use-screensize";
import Close from "@public/icons/close.svg";
import IconImage from "../icon-image";
import { usePathname } from "next/navigation";

import Backdrop from "@mui/material/Backdrop";

type ModalProps = {
  placeholder: React.ReactNode;
  children: React.ReactNode;
  closeIconStyle: string;
};

export default function ModalComponent({
  placeholder,
  children,
  closeIconStyle,
}: ModalProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleOpen = () => setIsModalOpen(true);
  const handleClose = () => setIsModalOpen(false);
  const { isMidScreen } = useScreensize();
  const pathname = usePathname();

  useEffect(() => {
    if (!isMidScreen) {
      setIsModalOpen(false);
    }
  }, [isMidScreen, setIsModalOpen, pathname]);

  useEffect(() => {
    setIsModalOpen(false);
  }, [setIsModalOpen, pathname]);

  return (
    <>
      <div className="p-0 m-0 w-fit" onClick={handleOpen}>
        {placeholder}
      </div>

      <Modal
        open={isModalOpen}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        style={{ background: "#5E5E5E80" }}
        closeAfterTransition={true}
        slots={{ backdrop: Backdrop }}
        slotProps={{
          backdrop: {
            style: { background: "#5E5E5E80" },
            onClick: handleClose,
          },
        }}
      >
        <Fade in={isModalOpen}>
          <div className="w-full h-full ">
            <div className="relative">
              {children}

              <div onClick={handleClose} className={`${closeIconStyle}`}>
                {" "}
                <IconImage src={Close} alt="close icon" />
              </div>
            </div>
          </div>
        </Fade>
      </Modal>
    </>
  );
}
