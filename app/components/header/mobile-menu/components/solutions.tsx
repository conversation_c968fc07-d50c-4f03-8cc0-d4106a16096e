"use client";
import { getClassNames } from "@/app/utils/helper";
import Image from "next/image";
import Link from "next/link";
import OpensourceIcon from "@public/icons/open-source.svg";
import NftIcon from "@public/icons/nft-icon.svg";
import { usePathname } from "next/navigation";

export default function Solutions() {
  const pathname = usePathname();
  return (
    <div className="space-y-8">
      <div>
        <Link href="/opensource">
          <div className="flex gap-x-3 items-center">
            <Image src={OpensourceIcon} alt="chevron" priority height={24} />{" "}
            <li
              className={getClassNames(
                pathname === "/opensource" ? "text-dark-brown font-title-bold" : ""
              )}
            >
              OpenSource
            </li>
          </div>
        </Link>
      </div>

      <div>
        <Link href="/nft">
          <div className="flex gap-x-3 items-center">
            <Image src={NftIcon} alt="chevron" priority height={24} />{" "}
            <li
              className={getClassNames(
                pathname === "/nft" ? "text-dark-brown font-title-bold" : ""
              )}
            >
              Nft
            </li>
          </div>
        </Link>
      </div>
    </div>
  );
}
