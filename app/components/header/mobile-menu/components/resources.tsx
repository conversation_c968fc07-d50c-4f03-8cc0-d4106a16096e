"use client";

import { getClassNames } from "@/app/utils/helper";
import Image from "next/image";
import Link from "next/link";

import BlogIcon from "@public/icons/blog-icon.svg";
import HelpCenterIcon from "@public/icons/logo_dark.svg";
import { usePathname } from "next/navigation";

export default function Resources() {
  const pathname = usePathname();
  return (
    <div className="space-y-8">
      {" "}
      <div>
        <Link href="https://blog.iKonnected.com/">
          <div className="flex gap-x-3 items-center">
            <Image src={BlogIcon} alt="chevron" priority height={24} />
            <li>Blog</li>{" "}
          </div>
        </Link>
      </div>
      <div>
        <Link href="https://iKonnected.notion.site/Help-Center-33a741a1ba0f43cb963fd4b4eb35d965">
          <div className="flex gap-x-3 items-center">
            {" "}
            <Image
              src={HelpCenterIcon}
              alt="chevron"
              priority
              height={24}
            />{" "}
            <li>Help Center</li>
          </div>
        </Link>
      </div>
    </div>
  );
}
