"use client";

import Image from "next/image";
import { getClassNames } from "@/app/utils/helper";
import { homeNav } from "@/app/utils/data";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Solutions from "./components/solutions";
import Resources from "./components/resources";

export default function MobileMenu() {
  const pathname = usePathname();
  return (
    <div className=" top-0 bg-creme w-full p-6">
      <ul className="space-y-9 text-[15px]">
        {homeNav.map(({ link, text }, index) => (
          <div
            key={index}
            className={getClassNames(
              pathname === link
                ? "text-dark-brown font-title-bold"
                : "text-light-brown font-title-semiBold"
            )}
          >
            {index === 0 || index === 3 ? (
              <Link href={link}>
                <li>{text}</li>
              </Link>
            ) : (
              <div className="space-y-8">
                <div className="flex justify-between items-center">
                  {" "}
                  <li>{text}</li>
                </div>

                {index === 1 ? <Solutions /> : <Resources />}
              </div>
            )}
          </div>
        ))}
      </ul>
    </div>
  );
}
