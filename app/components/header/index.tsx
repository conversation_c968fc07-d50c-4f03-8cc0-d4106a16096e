"use client";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
// icon
import Logo from "@public/icons/logo_dark.svg";
import ChevronDown from "@public/icons/chevron-down.svg";
import { getClassNames } from "@/app/utils/helper";
import useScrollPosition from "@/app/utils/hooks/use-scroll-position";
import { Fragment } from "react";
import ContextPopover from "../popover";
import TitleSmall from "../text/title/title-small";
import { homeNav } from "@/app/utils/data";

export default function Header() {
  const pathname = usePathname();
  const hasScrolled = useScrollPosition();

  return (
    <section
      className={getClassNames(
        "hidden lg:block py-3",
        "sticky top-0 bg-creme z-50",
        hasScrolled ? "shadow-custom" : "",
        "max-w-[2056px] mx-auto"
      )}
    >
      <div className="w-11/12 mx-auto flex justify-between items-center">
        <div>
          <Link href="/">
            <Image
              src={Logo}
              alt="iKonnected Logo"
              priority
              className="lg:w-40 xl:w-[100px]"
            />
          </Link>
        </div>

        <div className="flex gap-x-8 items-center">
          <ul className="flex gap-x-6 xl:gap-x-8 text-xl items-center">
            {homeNav.map(({ link, text }, index) => (
              <Fragment key={index}>
                {index === 0 || index === 3 ? (
                  <Link href={link}>
                    <li
                      className={getClassNames(
                        pathname === link
                          ? "text-dark-brown font-title-bold"
                          : "text-light-brown font-title-semiBold hover:bg-creme-100 hover:rounded-xl",
                        "px-3 py-2"
                      )}
                    >
                      {text}
                    </li>
                  </Link>
                ) : (
                  <ContextPopover
                    placeholder={
                      <li
                        className={getClassNames(
                          ((pathname.includes("/opensource") ||
                            pathname.includes("/nft")) &&
                            index === 1)
                            ? "text-dark-brown font-title-bold"
                            : "text-light-brown font-title-semiBold hover:bg-creme-100 hover:rounded-xl",
                          "px-3 py-2 cursor-pointer flex gap-x-2.5 items-center"
                        )}
                      >
                        {text}
                        <Image
                          src={ChevronDown}
                          alt="chevron"
                          priority
                          height={10}
                        />
                      </li>
                    }
                    containerStyle="-left-12 top-3 py-3 px-2.5 "
                  >
                    {index === 1 ? (
                      <div className="space-y-3 pr-8">
                        <div>
                          <Link href="/opensource">
                            <TitleSmall
                              text="Open Source"
                              className="md:text-lg lg:text-xl font-title-semiBold whitespace-nowrap"
                            />
                          </Link>
                        </div>
                        <div>
                          <Link href="/nft">
                            <TitleSmall
                              text="NFT"
                              className="md:text-lg lg:text-xl font-title-semiBold whitespace-nowrap"
                            />
                          </Link>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div>
                          <Link href="https://blog.iKonnected.com/">
                            <TitleSmall
                              text="Blog"
                              className="md:text-lg lg:text-xl font-title-semiBold whitespace-nowrap"
                            />
                          </Link>
                        </div>
                        <div>
                          <Link href="https://.notion.site/Help-Center-33a741a1ba0f43cb963fd4b4eb35d965">
                            <TitleSmall
                              text="Help Center"
                              className="md:text-lg lg:text-xl font-title-semiBold whitespace-nowrap"
                            />
                          </Link>
                        </div>
                      </div>
                    )}
                  </ContextPopover>
                )}
              </Fragment>
            ))}
          </ul>
         
        </div>
      </div>
    </section>
  );
}
