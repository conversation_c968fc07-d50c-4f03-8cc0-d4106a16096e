"use client";
import Image from "next/image";
import Logo from "@public/icons/logo_dark.svg";
import Menu from "@public/icons/mobile-menu.svg";
import { getClassNames } from "@/app/utils/helper";
import useScrollPosition from "@/app/utils/hooks/use-scroll-position";
import Link from "next/link";
import ModalComponent from "../modal";
import MobileMenu from "./mobile-menu";

export default function MobileHeader() {
  const hasScrolled = useScrollPosition();

  return (
    <section
      className={getClassNames(
        "py-4 sticky top-0 bg-creme z-50",
        "lg:hidden",
        hasScrolled ? "shadow-custom" : ""
      )}
    >
      <div className="w-11/12 mx-auto flex justify-between items-center">
        <div>
          <Link href="/">
            <Image src={Logo} alt="iKonnected Logo" priority height={25} />
          </Link>
        </div>

        <div>
          <ModalComponent
            placeholder={<Image src={Menu} alt="menu icon" priority />}
            closeIconStyle=" absolute right-[4.16%] top-4"
          >
            <MobileMenu />
          </ModalComponent>
        </div>
      </div>
    </section>
  );
}
