"use client";
import { MutableRefObject, ReactNode } from "react";
import Carousel from "react-multi-carousel";
import "react-multi-carousel/lib/styles.css";
// components
import TitleMedium from "@components/text/title/title-medium";
import CarouselNav from "@components/carousel/components/carousel-nav";
import CarouselComponent from "@components/carousel";

// utils

import { getClassNames } from "@/app/utils/helper";
import BodySmall from "@components/text/body/body-small";
import { StaticImageData } from "next/image";

type Props = {
  carouselRef: MutableRefObject<Carousel | null>;
  features: {
    title: string;
    body: string;
    bodyHeader?: string;
    background?: string;
    image: StaticImageData;
  }[];
  bodyText: string | ReactNode;
  title: string | ReactNode;
};

export default function FeaturesComponent({
  carouselRef,
  features,
  bodyText,
  title,
}: Props) {
  return (
    <section className="py-6 lg:py-16 space-y-9 lg:space-y-16 feature-container">
      <div className="w-11/12 mx-auto flex items-center justify-between ">
        <div className="pl-1.5 md:pl-3 lg:pl-10">
          <TitleMedium text={title} className="text-center" />
        </div>

        <CarouselNav carouselRef={carouselRef} features={features} />
      </div>

      <div className="w-11/12 mx-auto">
        <div className="md:w-11/12 xl:w-10/12 ">
          <BodySmall text={bodyText} className="leading-[160%] " />
        </div>
      </div>

      <div className="">
        <CarouselComponent
          carouselRef={carouselRef}
          features={features}
          imgContainerStyle={getClassNames(
            "pt-5 md:pt-12 md:px-9 xl:pt-10 px-5  rounded-xl",
            "md:rounded-2xl lg:rounded-[18px] flex flex-col items-center justify-end"
          )}
          cardContainerStyle="px-5 md:pl-8 lg:pl-12"
          imgStyle="lg:[309px] xl:h-[341px] lg:w-[210px] xl:[230px]"
        />
      </div>
    </section>
  );
}
