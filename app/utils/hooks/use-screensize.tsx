"use client";

import { useState, useEffect, useCallback } from "react";

export default function useScreensize() {
  const hasWindow = typeof window !== "undefined";
  const getWindowDimensions = useCallback(() => {
    const width = hasWindow ? window.innerWidth : null;
    const height = hasWindow ? window.innerHeight : null;
    return {
      width,
      height,
    };
  }, [hasWindow]);
  const [windowDimensions, setWindowDimensions] = useState(
    getWindowDimensions()
  );
  const { width } = windowDimensions;
  const [isMobile, setIsMobile] = useState(width && width <= 768);
  const [isMidScreen, setIsMidScreen] = useState(width && width <= 1024);

  useEffect(() => {
    if (hasWindow) {
      const handleResize = () => {
        const { width, height } = getWindowDimensions();

        setWindowDimensions({ width, height });
        setIsMobile(width && width < 768);
        setIsMidScreen(width && width < 1024);
      };

      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, [windowDimensions, hasWindow, getWindowDimensions]);

  return { isMobile, isMidScreen, ...windowDimensions };
  
}