@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  box-sizing: border-box;
}
body {
  margin: 0;
  padding: 0;
  background-color: #fef9f3;
  max-width: 100vw;
}
/* goldplay */
@font-face {
  font-family: "Goldplay Bold";
  src: local("Goldplay Bold"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ Bold.otf)
      format("truetype");
}
@font-face {
  font-family: "Goldplay Black";
  src: local("Goldplay Black"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ Black.otf)
      format("truetype");
}
@font-face {
  font-family: "Goldplay Medium";
  src: local("Goldplay Medium"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ Medium.otf)
      format("truetype");
}
@font-face {
  font-family: "Goldplay Light";
  src: local("Goldplay Light"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ Light.otf)
      format("truetype");
}
@font-face {
  font-family: "Goldplay Regular";
  src: local("Goldplay Regular"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ Regular.otf)
      format("truetype");
}
@font-face {
  font-family: "Goldplay SemiBold";
  src: local("Goldplay SemiBold"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ SemiBold.otf)
      format("truetype");
}
@font-face {
  font-family: "Goldplay Thin";
  src: local("Goldplay Thin"),
    url(../public/fonts/gold-play/Latinotype\ -\ Goldplay\ Thin.otf)
      format("truetype");
}

/* jeko */
@font-face {
  font-family: "Jeko Black";
  src: local("Jeko Black"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Black.otf) format("opentype");
}
@font-face {
  font-family: "Jeko ExtraBold";
  src: local("Jeko ExtraBold"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ ExtraBold.otf)
      format("opentype");
}
@font-face {
  font-family: "Jeko Bold";
  src: local("Jeko Bold"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Bold.otf) format("opentype");
}
@font-face {
  font-family: "Jeko ExtraLight";
  src: local("Jeko ExtraLight"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ ExtraLight.otf)
      format("opentype");
}
@font-face {
  font-family: "Jeko Light";
  src: local("Jeko Light"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Light.otf) format("opentype");
}
@font-face {
  font-family: "Jeko Medium";
  src: local("Jeko Medium"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Medium.otf) format("opentype");
}
@font-face {
  font-family: "Jeko Regular";
  src: local("Jeko Regular"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Regular.otf) format("opentype");
}
@font-face {
  font-family: "Jeko SemiBold";
  src: local("Jeko SemiBold"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ SemiBold.otf)
      format("opentype");
}
@font-face {
  font-family: "Jeko Thin";
  src: local("Jeko Thin"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Thin.otf) format("opentype");
}
@font-face {
  font-family: "Jeko Variable";
  src: local("Jeko Variable"),
    url(../public/fonts/jeko/EllenLuff\ -\ Jeko\ Variable.ttf)
      format("truetype");
}
.feature-container {
  position: relative;
}
.carousel-container {
  /* margin-left: 76px; */
  position: absolute;
  user-select: none;
}
.carousel-button-group {
  position: absolute;
  top: -20px;
  right: 0;
  z-index: 999;
}
.shadow-custom {
  box-shadow: 0px 1.5px 4px 0px rgba(214, 214, 214, 0.25);
}
.shadow-slider {
  filter: drop-shadow(0px 2px 4px rgba(20, 20, 43, 0.08));
}
.shadow-small {
  box-shadow: 1px 1px 1.5px 0px rgba(0, 0, 0, 0.25);
}
.white-shadow {
  box-shadow: 0px 2px 29px 0px rgba(0, 0, 0, 0.06);
}
.transition-max-height {
  transition: 0.3s ease-out, opacity 0.3s ease-out;
  overflow: hidden;
}

.max-height-0 {
  max-height: 0;
  opacity: 0;
}

.max-height-full {
  opacity: 1;
}

.rotate-0 {
  transform: rotate(0deg);
  transition: transform 0.5s ease-out;
}

.rotate-180 {
  transform: rotate(180deg);
  transition: transform 0.5s ease-out;
}
@media screen and (max-width: 640px) {
  .transition-max-height {
    transition: 0.2s ease-out, opacity 0.2s ease-out;
  }
  .rotate-180 {
    transition: transform 0.2s ease-out;
  }
  .rotate-0 {
    transition: transform 0.2s ease-out;
  }
}
