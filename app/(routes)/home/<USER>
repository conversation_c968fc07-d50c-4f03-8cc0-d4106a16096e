import Image from "next/image";
// components
import BodySmall from "@components/text/body/body-small";
import TitleLarge from "@components/text/title/title-large";
import IconImage from "@components/icon-image";
// icons
import Underline from "@public/icons/tech-underline.svg";
import HeroImage from "@public/icons/logo_dark.svg";
// utils
import { getClassNames } from "@/app/utils/helper";

export default function Hero() {
  return (
    <section className="relative">
   

      <div className="w-11/12 mx-auto pb-10 md:pb-20">
        <div className="space-y-9 pt-10 md:pt-16 lg:pt-24">
        <div className="lg:w-10/12 mx-auto z-10">
            <TitleLarge
              text={
                <>
                  Innovating{" "}
                  <span className="text-orange relative">
                    Internet Connection
                    <IconImage
                      src={Underline} // Use the new tech-focused underline
                      alt="iKonnected underline icon"
                      imageStyle="absolute bottom-0 left-0 w-full"
                    />
                  </span>
                  , Empowering everyone
                </>
              }
              className="md:text-center"
            />
          </div>


          <div className="md:w-[89%] lg:w-[65%] md:mx-auto">
            <BodySmall
              text="iKonnected helps you stayconnected, no matter where you are. We connect you to the internet..."
              className="md:text-center"
            />
          </div>
        </div>

        <div className="mt-1 w-7/12 sm:w-6/12 md:w-[39%] mx-auto">
          {" "}
          {/* <div className="flex items-center justify-center">
            <Image src={HeroImage} alt="Hero Image" priority={true} />
          </div> */}
        </div>
      </div>
    </section>
  );
}