import { privacyPolicy } from "@/app/utils/data";
import BodySmall from "@components/text/body/body-small";
import TitleLarge from "@components/text/title/title-large";
import TitleSmall from "@components/text/title/title-small";

export default function Privacy() {
  return (
    <section className="relative">
      <div className="w-11/12 mx-auto py-10 md:py-16 lg:py-24">
        <div className="space-y-9 ">
          <div className="lg:w-9/12 mx-auto z-10 space-y-4">
            <TitleLarge text="Privacy Policy" className="md:text-center" />
            <BodySmall
              text="Last updated, 21 July 2025"
              className="md:text-center"
            />
          </div>

          {privacyPolicy.map(({ question, answer, lists }, index) => (
            <div className="w-full z-10 space-y-4" key={index}>
              <TitleSmall text={question} className="" />
              <BodySmall text={answer} className="" />
              
              {lists?.map((item, index) => (
                <div key={`${index}`}>
                  <li className="font-body-semiBold tracking-[0.571px] leading-[160%] md:leading-[160%] lg:leading-[160%]">{item}</li>
                </div>
              ))}

            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
