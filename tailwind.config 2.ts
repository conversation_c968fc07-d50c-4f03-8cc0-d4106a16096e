import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        orange: "#FFA114",
        "light-blue": "#B6D6FF",
        "light-blue-100": "#003E78CC",
        "dark-blue": "#082F53",
        blue: "#2E84C0",

        "light-brown": "#74646C",
        "dark-brown": "#36282F",
        "dark-brown-400": "#36282F40",
        "dark-brown-100": "#37272F",

        "grey-100": "#D9D9D9",
        "grey-200": "#CBCBCB",
        "grey-300": "#D9DBE9",
        "grey-400": "#C4C4C4",
        "light-grey": "#E9E9E9",
        green: "#23A33B",
        "black-200": "#333333",
        "black-300": "#16213E",
        creme: "#FEF9F3",
        "creme-100": "#FBECDB",
        "creme-200": "#FFE3C2",
        neutral: "#EFF0F6",

        violet: "#D0C2FC80",

        "pink-peach": "#FEC3C9",
        "pink-peach-100": "#FEDBDF",
      },

      fontFamily: {
        "body-black": ["'Jeko Black'", "sans-serif"],
        "body-bold": ["'Jeko Bold'", "sans-serif"],
        "body-light": ["'Jeko Light'", "sans-serif"],
        "body-regular": ["'Jeko Regular'", "sans-serif"],
        "body-medium": ["'Jeko Medium'", "sans-serif"],
        "body-thin": ["'Jeko Thin'", "sans-serif"],
        "body-semiBold": ["'Jeko SemiBold'", "sans-serif"],
        "body-extraLight": ["'Jeko ExtraLight'", "sans-serif"],
        "body-extraBold": ["'Jeko ExtraBold'", "sans-serif"],
        "body-variable": ["'Jeko Variable'", "sans-serif"],

        "title-black": ["'Goldplay Black'", "sans-serif"],
        "title-bold": ["'Goldplay Bold'", "sans-serif"],
        "title-light": ["'Goldplay Light'", "sans-serif"],
        "title-medium": ["'Goldplay Medium'", "sans-serif"],
        "title-regular": ["'Goldplay Regular'", "sans-serif"],
        "title-semiBold": ["'Goldplay SemiBold'", "sans-serif"],
        "title-thin": ["'Goldplay Thin'", "sans-serif"],
      },
    },
  },
  plugins: [],
};
export default config;